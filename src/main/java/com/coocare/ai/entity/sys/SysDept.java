package com.coocare.ai.entity.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 部门管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_dept")
@Schema(description = "部门管理")
public class SysDept implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "部门ID")
    @TableId(value = "dept_id", type = IdType.ASSIGN_ID)
    private Long deptId;

    @Schema(description = "部门名称")
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 50, message = "部门名称长度不能超过50个字符")
    private String name;

    @Schema(description = "部门联系电话")
    @Size(max = 20, message = "部门联系电话长度不能超过20个字符")
    private String phone;

    @Schema(description = "部门联系邮箱")
    @Email(message = "部门联系邮箱格式不正确")
    @Size(max = 100, message = "部门联系邮箱长度不能超过100个字符")
    private String email;

    @Schema(description = "部门群组邮箱")
    @Email(message = "部门群组邮箱格式不正确")
    @Size(max = 100, message = "部门群组邮箱长度不能超过100个字符")
    private String groupEmail;

    @Schema(description = "部门办公电话")
    @Size(max = 20, message = "部门办公电话长度不能超过20个字符")
    private String officePhone;

    @Schema(description = "部门编码")
    @Size(max = 50, message = "部门编码长度不能超过50个字符")
    private String deptCode;

    @Schema(description = "部门类型")
    private String deptType;

    @Schema(description = "部门主管用户ID")
    private Long managerUserId;

    @Schema(description = "部门描述")
    @Size(max = 500, message = "部门描述长度不能超过500个字符")
    private String description;

    @Schema(description = "部门状态")
    private Integer status;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "修改人")
    private String updateBy;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "删除标志")
    @TableLogic
    private String delFlag;

    @Schema(description = "父级部门ID")
    private Long parentId;

}
