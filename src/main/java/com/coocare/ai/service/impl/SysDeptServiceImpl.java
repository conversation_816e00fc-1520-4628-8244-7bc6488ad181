package com.coocare.ai.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.entity.AiAgent;
import com.coocare.ai.entity.sys.SysDept;
import com.coocare.ai.entity.sys.SysUser;
import com.coocare.ai.entity.sys.dto.DeptQueryDTO;
import com.coocare.ai.entity.sys.vo.DeptVO;
import com.coocare.ai.mapper.SysDeptMapper;
import com.coocare.ai.mapper.SysUserMapper;
import com.coocare.ai.service.AiAgentService;
import com.coocare.ai.service.SysDeptService;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 部门领域服务实现
 * 提供部门树构建、分页、CRUD、主管设置与聚合视图（含智能体与用户统计）
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Service
@RequiredArgsConstructor
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements SysDeptService {

    private final AiAgentService aiAgentService;
    private final SysUserMapper userMapper;

    @Override
    public List<Tree<Long>> treeDept(Long parentId, String name) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .like(StrUtil.isNotBlank(name), SysDept::getName, name)
                .eq(SysDept::getDelFlag, "0")
                .orderByAsc(SysDept::getSortOrder);

        List<SysDept> deptList = this.list(wrapper);

        if (CollUtil.isEmpty(deptList)) {
            return CollUtil.newArrayList();
        }

        return TreeUtil.build(deptList, parentId, TreeNodeConfig.DEFAULT_CONFIG.setIdKey("deptId"),
                (treeNode, tree) -> {
                    tree.setId(treeNode.getDeptId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setName(treeNode.getName());
                    tree.putExtra("phone", treeNode.getPhone());
                    tree.putExtra("email", treeNode.getEmail());
                    tree.putExtra("groupEmail", treeNode.getGroupEmail());
                    tree.putExtra("officePhone", treeNode.getOfficePhone());
                    tree.putExtra("sortOrder", treeNode.getSortOrder());
                    tree.putExtra("createTime", treeNode.getCreateTime());
                    tree.putExtra("updateTime", treeNode.getUpdateTime());
                });
    }

    @Override
    public PageUtils pageInfo(PageDomain pageDomain, String name) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .like(StrUtil.isNotBlank(name), SysDept::getName, name)
                .eq(SysDept::getDelFlag, "0")
                .orderByAsc(SysDept::getSortOrder);

        Page<SysDept> page = this.page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), wrapper);
        return new PageUtils(page);
    }

    @Override
    public List<SysDept> getChildrenByParentId(Long parentId) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .eq(SysDept::getParentId, parentId)
                .eq(SysDept::getDelFlag, "0")
                .orderByAsc(SysDept::getSortOrder);
        return this.list(wrapper);
    }

    @Override
    public boolean hasChildren(Long deptId) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .eq(SysDept::getParentId, deptId)
                .eq(SysDept::getDelFlag, "0");
        return this.count(wrapper) > 0;
    }

    @Override
    public PageUtils pageInfoNew(PageDomain pageDomain, DeptQueryDTO queryDTO) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .like(StrUtil.isNotBlank(queryDTO.getName()), SysDept::getName, queryDTO.getName())
                .eq(StrUtil.isNotBlank(queryDTO.getDeptCode()), SysDept::getDeptCode, queryDTO.getDeptCode())
                .eq(queryDTO.getParentId() != null, SysDept::getParentId, queryDTO.getParentId())
                .eq(StrUtil.isNotBlank(queryDTO.getDeptType()), SysDept::getDeptType, queryDTO.getDeptType())
                .eq(queryDTO.getStatus() != null, SysDept::getStatus, queryDTO.getStatus())
                .eq(queryDTO.getManagerUserId() != null, SysDept::getManagerUserId, queryDTO.getManagerUserId())
                .eq(SysDept::getDelFlag, "0")
                .orderByAsc(SysDept::getSortOrder);

        Page<SysDept> page = this.page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), wrapper);
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDeptManager(Long deptId, Long userId) {
        // 更新部门主管字段
        SysDept dept = new SysDept();
        dept.setDeptId(deptId);
        dept.setManagerUserId(userId);
        boolean deptUpdated = this.updateById(dept);

        // 清除该部门其他用户的主管标识
        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getDeptId, deptId)
                .eq(SysUser::getIsDeptManager, 1)
                .eq(SysUser::getDelFlag, "0");
        List<SysUser> currentManagers = userMapper.selectList(wrapper);

        for (SysUser manager : currentManagers) {
            manager.setIsDeptManager(0);
            userMapper.updateById(manager);
        }

        // 设置新的部门主管标识
        SysUser newManager = userMapper.selectById(userId);
        if (newManager != null) {
            newManager.setIsDeptManager(1);
            newManager.setDeptId(deptId); // 确保用户属于该部门
            userMapper.updateById(newManager);
        }

        return deptUpdated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeDeptManager(Long deptId) {
        // 清除部门主管字段
        SysDept dept = new SysDept();
        dept.setDeptId(deptId);
        dept.setManagerUserId(null);
        boolean deptUpdated = this.updateById(dept);

        // 清除该部门所有用户的主管标识
        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getDeptId, deptId)
                .eq(SysUser::getIsDeptManager, 1)
                .eq(SysUser::getDelFlag, "0");
        List<SysUser> managers = userMapper.selectList(wrapper);

        for (SysUser manager : managers) {
            manager.setIsDeptManager(0);
            userMapper.updateById(manager);
        }

        return deptUpdated;
    }

    @Override
    public List<DeptVO> getDeptTreeWithAgents(Long parentId, boolean includeAgents) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .eq(SysDept::getDelFlag, "0")
                .orderByAsc(SysDept::getSortOrder);

        if (parentId != null) {
            wrapper.eq(SysDept::getParentId, parentId);
        }

        List<SysDept> deptList = this.list(wrapper);

        return deptList.stream().map(dept -> {
            DeptVO deptVO = new DeptVO();
            BeanUtils.copyProperties(dept, deptVO);

            // 获取部门主管信息
            if (dept.getManagerUserId() != null) {
                SysUser manager = userMapper.selectById(dept.getManagerUserId());
                deptVO.setManagerUser(manager);
            }

            // 获取AI智能体信息
            if (includeAgents) {
                List<AiAgent> agents = aiAgentService.getDeptAgents(dept.getDeptId());
                deptVO.setAgents(agents);
            }

            // 获取用户数量
            LambdaQueryWrapper<SysUser> userWrapper = Wrappers.lambdaQuery(SysUser.class)
                    .eq(SysUser::getDeptId, dept.getDeptId())
                    .eq(SysUser::getDelFlag, "0");
            long userCount = userMapper.selectCount(userWrapper);
            deptVO.setUserCount(userCount);

            // 检查是否有子部门
            deptVO.setHasChildren(hasChildren(dept.getDeptId()));

            // 递归获取子部门
            List<DeptVO> children = getDeptTreeWithAgents(dept.getDeptId(), includeAgents);
            deptVO.setChildren(children);

            return deptVO;
        }).collect(Collectors.toList());
    }

}
