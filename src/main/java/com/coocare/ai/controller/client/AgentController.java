package com.coocare.ai.controller.client;


import com.coocare.ai.config.domain.AjaxResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 智能体的应用
 * @author: Adam
 * @create: 2025-08-11 14:22
 **/

@Tag(name = "智能体的应用")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/rest/agent")
public class AgentController {


    @SneakyThrows
    @GetMapping("messageEnd/{agentId}/{conversationId}")
    public AjaxResult<?> messageEnd(
            @PathVariable("agentId") Long agentId,
            @PathVariable("conversationId") String conversationId) {
        return AjaxResult.ok();
    }




}
