//package com.coocare.ai.controller;
//
//import cn.dev33.satoken.annotation.SaIgnore;
//import cn.dev33.satoken.stp.StpUtil;
//import com.coocare.ai.config.domain.AjaxResult;
//import com.coocare.ai.service.SysUserService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.Map;
//
//@Tag(name = "授权登录")
//@RestController
//@RequestMapping("/auth")
//@RequiredArgsConstructor
//@Slf4j
//public class AuthController {
//
//	private final SysUserService userService;
//
//	@SaIgnore
//	@Operation(summary = "登录")
//	@PostMapping("login")
//	public AjaxResult<Map<String, Object>> login(@Parameter(description = "登录名") @RequestParam(value = "username", required = false) String username,
//												 @Parameter(description = "验证码") @RequestParam(value = "code", required = false) String code,
//												 @Parameter(description = "密码") @RequestParam(value = "password", required = false) String password,
//												 @Parameter(description = "验证码的UUID") @RequestParam(value = "uuid", required = false) String uuid) {
//		log.info("用户{}开始进行登录", username);
//		return AjaxResult.ok(userService.login(username,password));
//	}
//
//	@Operation(summary = "退出登录")
//	@PostMapping("logout")
//	public AjaxResult<Object> logout() {
//		StpUtil.logout();
//		return AjaxResult.ok();
//	}
//}
