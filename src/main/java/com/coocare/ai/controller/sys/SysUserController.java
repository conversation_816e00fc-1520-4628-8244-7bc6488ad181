package com.coocare.ai.controller.sys;

import cn.hutool.core.util.ObjectUtil;
import com.coocare.ai.config.auth.reource.util.SecurityUtils;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.plugins.logging.annotation.Logging;
import com.coocare.ai.entity.sys.SysUser;
import com.coocare.ai.entity.sys.dto.DeptManagerDTO;
import com.coocare.ai.entity.sys.dto.UserDTO;
import com.coocare.ai.entity.sys.dto.UserDeptDTO;
import com.coocare.ai.entity.sys.dto.UserRoleAssignDTO;
import com.coocare.ai.entity.sys.vo.UserDeptVO;
import com.coocare.ai.service.SysUserService;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户管理控制器
 * 提供用户的查询、新增、修改、删除、密码管理、角色分配、部门管理等接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Tag(name = "用户管理")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys/user")
public class SysUserController {

    private final SysUserService userService;

    /**
     * 分页查询用户列表
     * @param searchWord 搜索关键词
     * @param status 用户状态
     * @param pageDomain 分页参数
     * @return 用户分页列表
     */
    @Operation(summary = "分页查询用户列表", description = "根据搜索条件和状态分页查询用户信息", operationId = "sysUser_page")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("page")
//    @PreAuthorize("hasAuthority('SCOPE_message.write')")
    public AjaxResult<?> getUserList(
            @Parameter(description = "搜索关键词，支持用户名、姓名、邮箱模糊搜索") String searchWord,
            @Parameter(description = "用户状态，true-启用，false-禁用") Boolean status,
            @Parameter(description = "分页参数") PageDomain pageDomain) {
        PageUtils page = userService.queryPage(searchWord, status, pageDomain);
        return AjaxResult.ok(page);
    }

    /**
     * 新增用户
     * @param userDTO 用户信息
     * @return 新增结果
     */
    @Operation(summary = "新增用户", description = "创建新用户，需要提供用户名、手机号、邮箱、密码等基本信息", operationId = "sysUser_create")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "新增成功"),
        @ApiResponse(responseCode = "400", description = "参数校验失败或用户名已存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping()
    public AjaxResult<?> saveUser(
            @Parameter(description = "用户信息", required = true) @RequestBody UserDTO userDTO) {
        SysUser sysUser = userService.queryUserByName(userDTO.getUsername());
        if (ObjectUtil.isNotNull(sysUser)) {
            return AjaxResult.failed("user.name.exist");
        }
        return AjaxResult.ok(userService.saveUser(userDTO));
    }

    /**
     * 修改用户信息
     * @param userDTO 用户信息
     * @return 修改结果
     */
    @Operation(summary = "修改用户信息", description = "根据用户ID更新用户的基本信息", operationId = "sysUser_update")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "修改成功"),
        @ApiResponse(responseCode = "400", description = "参数校验失败"),
        @ApiResponse(responseCode = "404", description = "用户不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping()
    public AjaxResult<?> updateUser(
            @Parameter(description = "用户信息", required = true) @RequestBody UserDTO userDTO) {
        return AjaxResult.ok(userService.updateUser(userDTO));
    }

    /**
     * 删除用户
     * @param id 用户ID
     * @return 删除结果
     */
    @Operation(summary = "删除用户", description = "根据用户ID删除用户，注意：删除操作不可恢复", operationId = "sysUser_delete")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "404", description = "用户不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("/{id}")
//    @SaCheckPermission("sys_user_del")
    public AjaxResult<?> deleteUser(
            @Parameter(description = "用户ID", required = true) @PathVariable Long id) {
        return AjaxResult.ok(userService.removeById(id));
    }

    @Operation(summary = "用户修改密码", description = "用户自行修改登录密码，需提供旧密码与新密码", operationId = "sysUser_changePass")
    @PutMapping("changePass")
    public AjaxResult<?> changePass(@RequestParam("oldPass") String oldPass, @RequestParam("password") String password) {
        if (!userService.changeUserPass(oldPass, password)) {
            return AjaxResult.failed("密码错误");
        } else {
            return AjaxResult.ok();
        }
    }

    /**
     * 校验密码接口
     * @param password 需要校验的密码
     * @return R 返回结果对象，包含校验密码操作的结果信息
     */
    @Operation(summary = "校验密码接口", description = "校验当前登录用户输入的密码是否正确", operationId = "sysUser_checkPassword")
    @PostMapping("/check")
    public AjaxResult<?> check(String password) {
        return AjaxResult.success(userService.checkPassword(password));
    }

    @Operation(summary = "重置密码", description = "管理员为指定用户重置密码", operationId = "sysUser_resetPassword")
    @PutMapping("/resetPass/{id}")
    public AjaxResult<?> reset(@PathVariable("id") Long id) {
        return AjaxResult.ok(userService.resetPassword(id));
    }

    @Operation(summary = "状态切换", description = "启用或禁用指定用户账号", operationId = "sysUser_changeStatus")
    @PutMapping("/change/{id}/{status}")
    public AjaxResult<?> change(@PathVariable("id") Long id, @PathVariable("status") Boolean status) {
        return AjaxResult.success(userService.change(id, status));
    }

    @Operation(summary = "用户分配角色")
    @PutMapping("/assignRoles")
    public AjaxResult<?> assignRoles(@Valid @RequestBody UserRoleAssignDTO dto) {
        return AjaxResult.success(userService.assignRoles(dto));
    }

    @Operation(summary = "获取当前登录的用户信息")
    @GetMapping("/loginInfo")
    public AjaxResult<?> loginInfo() {
        return AjaxResult.ok(SecurityUtils.getUser());
    }


    @Operation(summary = "根据用户id获取角色信息")
    @GetMapping("/getRolesByUser/{id}")
    public AjaxResult<?> getRolesByUser(@PathVariable("id") Long id) {
        return AjaxResult.ok(userService.getRolesByUser(id));
    }


    /**
     * 根据部门ID查询用户列表
     * @param deptId 部门ID
     * @param includeChildren 是否包含子部门用户
     * @return 用户列表
     */
    @Operation(summary = "根据部门ID查询用户列表")
    @GetMapping("/dept/{deptId}")
    public AjaxResult<?> getUsersByDeptId(@PathVariable Long deptId,
                                          @RequestParam(defaultValue = "false") boolean includeChildren) {
        List<UserDeptVO> users = userService.getUsersByDeptId(deptId, includeChildren);
        return AjaxResult.ok(users);
    }

    /**
     * 分页查询部门用户
     * @param deptId 部门ID
     * @param searchWord 搜索关键词
     * @param includeChildren 是否包含子部门用户
     * @param pageDomain 分页参数
     * @return 分页结果
     */
    @Operation(summary = "分页查询部门用户")
    @GetMapping("/dept/{deptId}/page")
    public AjaxResult<?> queryDeptUsersPage(@PathVariable Long deptId,
                                            @RequestParam(required = false) String searchWord,
                                            @RequestParam(defaultValue = "false") boolean includeChildren,
                                            PageDomain pageDomain) {
        PageUtils page = userService.queryDeptUsersPage(deptId, searchWord, includeChildren, pageDomain);
        return AjaxResult.ok(page);
    }

    /**
     * 设置用户部门
     * @param userDeptDTO 用户部门DTO
     * @return 设置结果
     */
    @Operation(summary = "设置用户部门")
    @Logging("设置用户部门")
    @PostMapping("/dept/set")
    public AjaxResult<?> setUserDept(@Valid @RequestBody UserDeptDTO userDeptDTO) {
        return AjaxResult.ok(userService.setUserDept(userDeptDTO));
    }

    /**
     * 批量设置用户部门
     * @param userDeptDTO 用户部门DTO
     * @return 设置结果
     */
    @Operation(summary = "批量设置用户部门")
    @Logging("批量设置用户部门")
    @PostMapping("/dept/set/batch")
    public AjaxResult<?> batchSetUserDept(@Valid @RequestBody UserDeptDTO userDeptDTO) {
        return AjaxResult.ok(userService.batchSetUserDept(userDeptDTO));
    }


    /**
     * 设置部门主管
     * @param deptManagerDTO 部门主管DTO
     * @return 设置结果
     */
    @Operation(summary = "设置部门主管")
    @Logging("设置部门主管")
    @PostMapping("/dept/manager/set")
    public AjaxResult<?> setDeptManager(@Valid @RequestBody DeptManagerDTO deptManagerDTO) {
        return AjaxResult.ok(userService.setDeptManager(deptManagerDTO));
    }

    /**
     * 移除部门主管
     * @param deptManagerDTO 部门主管DTO
     * @return 移除结果
     */
    @Operation(summary = "移除部门主管")
    @Logging("移除部门主管")
    @PostMapping("/dept/manager/remove")
    public AjaxResult<?> removeDeptManager(@Valid @RequestBody DeptManagerDTO deptManagerDTO) {
        return AjaxResult.ok(userService.removeDeptManager(deptManagerDTO));
    }

    /**
     * 获取部门主管列表
     * @param deptId 部门ID
     * @return 部门主管列表
     */
    @Operation(summary = "获取部门主管列表")
    @GetMapping("/dept/{deptId}/managers")
    public AjaxResult<?> getDeptManagers(@PathVariable Long deptId) {
        List<UserDeptVO> managers = userService.getDeptManagers(deptId);
        return AjaxResult.ok(managers);
    }

    /**
     * 转移用户到其他部门
     * @param userIds 用户ID列表
     * @param fromDeptId 原部门ID
     * @param toDeptId 目标部门ID
     * @return 转移结果
     */
    @Operation(summary = "转移用户到其他部门")
    @Logging("转移用户到其他部门")
    @PostMapping("/dept/transfer")
    public AjaxResult<?> transferUsersToDept(@RequestParam List<Long> userIds,
                                             @RequestParam Long fromDeptId,
                                             @RequestParam Long toDeptId) {
        return AjaxResult.ok(userService.transferUsersToDept(userIds, fromDeptId, toDeptId));
    }

    /**
     * 从部门中移除用户
     * @param userIds 用户ID列表
     * @param deptId 部门ID
     * @return 移除结果
     */
    @Operation(summary = "从部门中移除用户")
    @Logging("从部门中移除用户")
    @PostMapping("/dept/remove")
    public AjaxResult<?> removeUsersFromDept(@RequestParam List<Long> userIds,
                                             @RequestParam Long deptId) {
        return AjaxResult.ok(userService.removeUsersFromDept(userIds, deptId));
    }
}
