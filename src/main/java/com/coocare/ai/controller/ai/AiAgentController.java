package com.coocare.ai.controller.ai;


import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.plugins.logging.annotation.Logging;
import com.coocare.ai.entity.AiAgent;
import com.coocare.ai.service.AiAgentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 智能体管理控制器
 * 提供智能体列表、启用状态、部门绑定等接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */

@Tag(name = "智能体管理")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/agents")
public class AiAgentController {

    private final AiAgentService aiAgentService;

    /**
     * 获取所有可用的智能体列表
     * @return 智能体列表
     */
    @Operation(summary = "获取所有可用的智能体列表", description = "获取系统中所有可用的智能体列表", operationId = "aiAgent_all")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "500", description = "获取失败")
    })
    @GetMapping("/all")
    public AjaxResult<List<AiAgent>> getAllAvailableAgents() {
        try {
            List<AiAgent> agents = aiAgentService.getAllAvailableAgents();
            return AjaxResult.ok(agents);
        } catch (Exception e) {
            log.error("获取所有可用智能体失败", e);
            return AjaxResult.failed("获取智能体列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取已启用的智能体ID列表
     * @return 已启用的智能体ID列表
     */
    @Operation(summary = "获取已启用的智能体ID列表", description = "获取系统中已启用的智能体ID列表", operationId = "aiAgent_enabledIds")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "500", description = "获取失败")
    })
    @GetMapping("/enabled")
    public AjaxResult<List<Long>> getEnabledAgents() {
        try {
            List<Long> enabledAgents = aiAgentService.getEnabledAgents();
            return AjaxResult.ok(enabledAgents);
        } catch (Exception e) {
            log.error("获取已启用智能体列表失败", e);
            return AjaxResult.failed("获取已启用智能体列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取已启用的智能体详细信息列表
     * @return 已启用的智能体详细信息列表
     */
    @Operation(summary = "获取已启用的智能体详细信息", description = "获取系统中已启用的智能体详细信息列表", operationId = "aiAgent_enabledDetails")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "500", description = "获取失败")
    })
    @GetMapping("/enabled/details")
    public AjaxResult<List<AiAgent>> getEnabledAgentDetails() {
        try {
            List<AiAgent> enabledAgents = aiAgentService.getEnabledAgentDetails();
            return AjaxResult.ok(enabledAgents);
        } catch (Exception e) {
            log.error("获取已启用智能体详细信息失败", e);
            return AjaxResult.failed("获取已启用智能体详细信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取部门关联的AI智能体列表
     * @param deptId 部门ID
     * @return AI智能体列表
     */
    @Operation(summary = "获取部门关联的AI智能体列表", description = "根据部门ID查询已关联的AI智能体列表", operationId = "aiAgent_deptAgents")
    @GetMapping("/{deptId}/agents")
    public AjaxResult<?> getDeptAgents(@PathVariable Long deptId) {
        List<AiAgent> agents = aiAgentService.getDeptAgents(deptId);
        return AjaxResult.ok(agents);
    }

    /**
     * 设置部门AI智能体关联
     * @param deptId 部门ID
     * @param agentIds AI智能体ID列表
     * @param defaultAgentId 默认AI智能体ID
     * @return success/false
     */
    @Operation(summary = "设置部门AI智能体关联", description = "为指定部门设置关联的AI智能体及默认智能体", operationId = "aiAgent_setDeptAgents")
    @Logging("设置部门AI智能体关联")
    @PostMapping("/{deptId}/agents")
    public AjaxResult<?> setDeptAgents(@PathVariable Long deptId,
                                       @RequestParam List<Long> agentIds,
                                       @RequestParam(required = false) Long defaultAgentId) {
        return AjaxResult.ok(aiAgentService.setDeptAgents(deptId, agentIds, defaultAgentId));
    }
}
