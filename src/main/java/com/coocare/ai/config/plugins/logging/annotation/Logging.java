package com.coocare.ai.config.plugins.logging.annotation;
/**
 * 统一日志注解
 * 标注在类/方法上，配合 LoggingAspect 实现入参/耗时/异常记录
 *
 * <AUTHOR>
 * @since 2025-08-19
 */

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
public @interface Logging {

    /**
     * 默认无参输入
     */
    String value() default "暂无标题";

    /**
     * Title 默认输入
     */
    String title() default "暂无标题";

}
